#!/usr/bin/env python3
"""
Script pour analyser l'exécutable Royal Predictor v2.1
et extraire des informations sur son fonctionnement
"""

import re
import os

def extract_strings(filename, min_length=4):
    """Extrait les chaînes de caractères lisibles d'un fichier binaire"""
    strings = []

    with open(filename, 'rb') as f:
        data = f.read()

    # Méthode simple : recherche de séquences de caractères imprimables
    current_string = b''
    for byte in data:
        if 32 <= byte <= 126:  # Caractères ASCII imprimables
            current_string += bytes([byte])
        else:
            if len(current_string) >= min_length:
                try:
                    string = current_string.decode('ascii')
                    strings.append(string)
                except:
                    pass
            current_string = b''

    # Traiter la dernière chaîne si elle existe
    if len(current_string) >= min_length:
        try:
            string = current_string.decode('ascii')
            strings.append(string)
        except:
            pass

    return strings

def analyze_royal_predictor():
    """Analyse l'exécutable Royal Predictor"""
    exe_path = "Royal Predictor v2.1.exe"
    
    if not os.path.exists(exe_path):
        print(f"Erreur: {exe_path} non trouvé")
        return
    
    print("=== ANALYSE DE ROYAL PREDICTOR v2.1 ===\n")
    
    # Extraction des chaînes
    print("Extraction des chaînes de caractères...")
    strings = extract_strings(exe_path, min_length=6)
    
    # Mots-clés liés aux prédictions
    prediction_keywords = [
        'predict', 'prediction', 'forecast', 'royal', 'algorithm', 'model',
        'data', 'analysis', 'calculate', 'compute', 'result', 'probability',
        'statistics', 'math', 'number', 'random', 'pattern', 'trend',
        'neural', 'machine', 'learning', 'ai', 'artificial', 'intelligence',
        'regression', 'classification', 'clustering', 'optimization',
        'numpy', 'scipy', 'pandas', 'sklearn', 'tensorflow', 'pytorch'
    ]
    
    # Recherche de chaînes pertinentes
    relevant_strings = []
    for string in strings:
        string_lower = string.lower()
        for keyword in prediction_keywords:
            if keyword in string_lower:
                relevant_strings.append(string)
                break
    
    print(f"\n=== CHAÎNES LIÉES AUX PRÉDICTIONS ({len(relevant_strings)} trouvées) ===")
    for i, string in enumerate(relevant_strings[:50], 1):
        print(f"{i:2d}. {string}")
    
    # Recherche de fonctions Python
    python_functions = []
    for string in strings:
        if any(pattern in string for pattern in ['def ', 'class ', 'import ', 'from ']):
            python_functions.append(string)
    
    print(f"\n=== FONCTIONS/IMPORTS PYTHON ({len(python_functions)} trouvées) ===")
    for i, func in enumerate(python_functions[:30], 1):
        print(f"{i:2d}. {func}")
    
    # Recherche de noms de fichiers
    file_patterns = []
    for string in strings:
        if any(ext in string for ext in ['.py', '.pyd', '.dll', '.json', '.csv', '.txt', '.dat']):
            file_patterns.append(string)
    
    print(f"\n=== FICHIERS RÉFÉRENCÉS ({len(file_patterns)} trouvés) ===")
    for i, file_ref in enumerate(file_patterns[:30], 1):
        print(f"{i:2d}. {file_ref}")
    
    # Recherche de messages d'erreur ou d'interface
    ui_strings = []
    for string in strings:
        if any(pattern in string.lower() for pattern in [
            'error', 'warning', 'success', 'failed', 'button', 'window',
            'dialog', 'message', 'input', 'output', 'result', 'calculate'
        ]):
            ui_strings.append(string)
    
    print(f"\n=== MESSAGES D'INTERFACE ({len(ui_strings)} trouvés) ===")
    for i, ui_string in enumerate(ui_strings[:30], 1):
        print(f"{i:2d}. {ui_string}")

if __name__ == "__main__":
    analyze_royal_predictor()
